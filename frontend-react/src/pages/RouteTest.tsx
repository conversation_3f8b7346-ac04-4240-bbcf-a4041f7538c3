import React from 'react'
import { Link, useLocation, useParams } from 'react-router-dom'
import { generateNavUrls, extractCountryFromUrl, getBasePathFromUrl } from '../utils/countryUrl'

const RouteTest: React.FC = () => {
  const location = useLocation()
  const { country_code } = useParams<{ country_code?: string }>()
  const navUrls = generateNavUrls()
  
  const currentCountry = extractCountryFromUrl(location.pathname) || 'US'
  const basePath = getBasePathFromUrl(location.pathname)

  const testUrls = [
    { label: 'Home (US)', url: '/home' },
    { label: 'Home (CA)', url: '/ca/home' },
    { label: 'Stores (US)', url: '/stores' },
    { label: 'Stores (UK)', url: '/uk/stores' },
    { label: 'Coupons (US)', url: '/coupons' },
    { label: 'Coupons (DE)', url: '/de/coupons' },
    { label: 'Blog (US)', url: '/blog' },
    { label: 'Blog (FR)', url: '/fr/blog' },
    { label: 'About (US)', url: '/about' },
    { label: 'About (AU)', url: '/au/about' },
    { label: 'Contact (US)', url: '/contact' },
    { label: 'Contact (CA)', url: '/ca/contact' },
    { label: 'Invalid Country', url: '/invalid/about' }
  ]

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-purple-600 mb-8">Route Testing Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Current Route Info */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Current Route Info</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Full Path:</strong> {location.pathname}</p>
              <p><strong>Country Code (param):</strong> {country_code || 'None'}</p>
              <p><strong>Detected Country:</strong> {currentCountry}</p>
              <p><strong>Base Path:</strong> {basePath}</p>
              <p><strong>Search:</strong> {location.search || 'None'}</p>
            </div>
          </div>

          {/* Generated URLs */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Generated URLs for Current Country</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Home:</strong> {navUrls.home}</p>
              <p><strong>Stores:</strong> {navUrls.stores}</p>
              <p><strong>Coupons:</strong> {navUrls.coupons}</p>
              <p><strong>Blog:</strong> {navUrls.blog}</p>
              <p><strong>About:</strong> {navUrls.about}</p>
              <p><strong>Contact:</strong> {navUrls.contact}</p>
            </div>
          </div>
        </div>

        {/* Test Links */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Test Links</h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {testUrls.map((item, index) => (
              <Link
                key={index}
                to={item.url}
                className="block p-3 text-center bg-purple-100 hover:bg-purple-200 rounded-lg transition-colors duration-200 text-sm"
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4 text-yellow-800">Testing Instructions</h2>
          <ol className="list-decimal list-inside space-y-2 text-sm text-yellow-700">
            <li>Click on different test links to verify routing works</li>
            <li>Use the country selector in the header to change countries</li>
            <li>Verify that URLs change correctly when switching countries</li>
            <li>Check that invalid country codes redirect properly</li>
            <li>Ensure US routes don't have country codes in URLs</li>
            <li>Test that non-US routes have lowercase country codes</li>
          </ol>
        </div>
      </div>
    </div>
  )
}

export default RouteTest
