import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './styles/globals.css'
import { createBrowserRouter, RouterProvider, Navigate, redirect } from 'react-router-dom'
import Home from './pages/Home'
import LandingPage from './pages/LandingPage'
import Login from './pages/auth/Login'
import Register from './pages/auth/Register'
import Profile from './pages/profile'
import Stores from './pages/stores'
import StoreDetail from './pages/stores/detail'
import Orders from './pages/orders'
import ShoppingTrips from './pages/ShoppingTrips'
import NotFound from './pages/NotFound'
import Blog from './pages/Blog'
import BlogDetail from './pages/BlogDetail'
import Coupons from './pages/coupons/Coupons'
import AboutUs from './pages/AboutUs'
import Contact from './pages/Contact'
import HelpCenter from './pages/HelpCenter'
import HowItWorks from './pages/HowItWorks'
import TermsOfService from './pages/TermsOfService'
import PrivacyPolicy from './pages/PrivacyPolicy'
import { AuthProvider } from './contexts/AuthContext'
import { StoreProvider } from './contexts/StoreContext'
import { generateNavUrls } from './utils/countryUrl'
import CountryRouteGuard from './components/CountryRouteGuard'
import RouteTest from './pages/RouteTest'

// 检查用户是否已登录的函数
const isUserLoggedIn = () => {
  return localStorage.getItem('token') && localStorage.getItem('user');
};

// 根路径的处理函数：未登录用户显示着陆页，已登录用户重定向到country-specific home
const RootHandler = () => {
  if (isUserLoggedIn()) {
    const navUrls = generateNavUrls()
    return <Navigate to={navUrls.home} replace />
  }
  return <LandingPage />
};


const router = createBrowserRouter([{
    path: "/",
    element: <App />,
    errorElement: (
      <React.StrictMode>
        <AuthProvider>
          <StoreProvider>
            <NotFound />
          </StoreProvider>
        </AuthProvider>
      </React.StrictMode>
    ),
    children: [
      { index: true, element: <RootHandler /> },

      // Non-country specific routes first
      {
        path: "store/:unique_name",
        loader: ({ params }) => {
          return redirect(`/stores/${params.unique_name}`)
        }
      },
      {
        path: "auth",
        children: [
          { path: "login", element: <Login /> },
          { path: "register", element: <Register /> }
        ]
      },
      { path: "profile", element: <Profile /> },
      { path: "orders", element: <Orders /> },
      { path: "shopping-trips", element: <ShoppingTrips /> },

      // Base routes (US - no country code)
      { path: "home", element: <Home /> },
      {
        path: "stores",
        children: [
          { index: true, element: <Stores /> },
          { path: ":unique_name", element: <StoreDetail /> }
        ]
      },
      {
        path: "blog",
        children: [
          { index: true, element: <Blog /> },
          { path: ":id", element: <BlogDetail /> }
        ]
      },
      { path: "coupons", element: <Coupons /> },
      { path: "about", element: <AboutUs /> },
      { path: "contact", element: <Contact /> },
      { path: "help", element: <HelpCenter /> },
      { path: "how-it-works", element: <HowItWorks /> },
      { path: "terms", element: <TermsOfService /> },
      { path: "privacy", element: <PrivacyPolicy /> },
      { path: "route-test", element: <RouteTest /> },

      // Country-specific routes (must come after base routes)
      { path: ":country_code/home", element: <CountryRouteGuard><Home /></CountryRouteGuard> },
      {
        path: ":country_code/stores",
        children: [
          { index: true, element: <CountryRouteGuard><Stores /></CountryRouteGuard> },
          { path: ":unique_name", element: <CountryRouteGuard><StoreDetail /></CountryRouteGuard> }
        ]
      },
      {
        path: ":country_code/blog",
        children: [
          { index: true, element: <CountryRouteGuard><Blog /></CountryRouteGuard> },
          { path: ":id", element: <CountryRouteGuard><BlogDetail /></CountryRouteGuard> }
        ]
      },
      { path: ":country_code/coupons", element: <CountryRouteGuard><Coupons /></CountryRouteGuard> },
      { path: ":country_code/about", element: <CountryRouteGuard><AboutUs /></CountryRouteGuard> },
      { path: ":country_code/contact", element: <CountryRouteGuard><Contact /></CountryRouteGuard> },
      { path: ":country_code/help", element: <CountryRouteGuard><HelpCenter /></CountryRouteGuard> },
      { path: ":country_code/how-it-works", element: <CountryRouteGuard><HowItWorks /></CountryRouteGuard> },
      { path: ":country_code/terms", element: <CountryRouteGuard><TermsOfService /></CountryRouteGuard> },
      { path: ":country_code/privacy", element: <CountryRouteGuard><PrivacyPolicy /></CountryRouteGuard> },
      { path: ":country_code/route-test", element: <CountryRouteGuard><RouteTest /></CountryRouteGuard> }
    ],
  },
], {
  basename: "/",
  future: {
    v7_relativeSplatPath: true
  }
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
)
