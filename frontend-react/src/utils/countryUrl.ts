// Utility functions for managing country-specific URLs

// Supported country codes
export const supportedCountryCodes = [
  'US', 'CA', 'AT', 'DE', 'FR', 'UK', 'ES', 'IT', 'MX', 'IN',
  'PT', 'PL', 'NL', 'AU', 'BE', 'BR', 'CL', 'HK', 'AR', 'FI',
  'SE', 'SG', 'DK', 'NO', 'PE', 'CZ', 'TR', 'IE', 'SA', 'CH',
  'MY', 'RO', 'AE', 'NZ'
]

// Pages that should have country-specific URLs
export const countrySpecificPages = [
  'home',
  'stores',
  'coupons',
  'blog',
  'about',
  'contact',
  'help',
  'how-it-works',
  'terms',
  'privacy'
]

// Pages that should fetch different data based on country
export const dataFetchingPages = ['home', 'stores']

/**
 * Check if a country code is valid
 */
export const isValidCountryCode = (code: string | undefined): boolean => {
  return code ? supportedCountryCodes.includes(code.toUpperCase()) : false
}

/**
 * Get the current country code from localStorage or default to US
 */
export const getCurrentCountryCode = (): string => {
  try {
    const savedCountry = localStorage.getItem('selectedCountry')
    if (savedCountry) {
      const country = JSON.parse(savedCountry)
      if (country.code && isValidCountryCode(country.code)) {
        return country.code.toUpperCase()
      }
    }
  } catch (error) {
    console.error('Error parsing saved country:', error)
  }
  return 'US'
}

/**
 * Generate a country-specific URL
 * @param path - The base path (e.g., '/home', '/stores', '/blog/123')
 * @param countryCode - The country code (optional, defaults to current country)
 * @returns The country-specific URL
 */
export const generateCountryUrl = (path: string, countryCode?: string): string => {
  const country = countryCode || getCurrentCountryCode()
  let cleanPath = path.startsWith('/') ? path.slice(1) : path

  // Remove any existing country code from the path
  const pathSegments = cleanPath.split('/')
  if (pathSegments.length > 0 && isValidCountryCode(pathSegments[0])) {
    pathSegments.shift() // Remove the country code
    cleanPath = pathSegments.join('/')
  }

  // For US, don't add country code to URL
  if (country === 'US') {
    return `/${cleanPath}`
  }

  // Extract the base page from the clean path
  const basePageSegments = cleanPath.split('/')
  const basePage = basePageSegments[0]

  // Only add country code for supported pages
  if (countrySpecificPages.includes(basePage)) {
    return `/${country.toLowerCase()}/${cleanPath}`
  }

  return `/${cleanPath}`
}

/**
 * Extract country code from URL path
 * @param pathname - The current pathname
 * @returns The country code if found, otherwise null
 */
export const extractCountryFromUrl = (pathname: string): string | null => {
  const segments = pathname.split('/').filter(Boolean)

  if (segments.length >= 1) {
    const firstSegment = segments[0].toUpperCase()
    if (isValidCountryCode(firstSegment)) {
      return firstSegment
    }
  }

  return null
}

/**
 * Get the base path without country code
 * @param pathname - The current pathname
 * @returns The base path without country code
 */
export const getBasePathFromUrl = (pathname: string): string => {
  const segments = pathname.split('/').filter(Boolean)

  if (segments.length >= 1) {
    const firstSegment = segments[0].toUpperCase()
    if (isValidCountryCode(firstSegment)) {
      // Remove country code and return the rest
      return '/' + segments.slice(1).join('/')
    }
  }

  return pathname
}

/**
 * Check if the current page should fetch different data based on country
 * @param pathname - The current pathname
 * @returns True if the page should fetch country-specific data
 */
export const shouldFetchCountryData = (pathname: string): boolean => {
  const basePath = getBasePathFromUrl(pathname)
  const segments = basePath.split('/').filter(Boolean)
  const basePage = segments[0] || 'home'

  return dataFetchingPages.includes(basePage)
}

/**
 * Generate navigation URLs for different pages
 */
export const generateNavUrls = (countryCode?: string) => {
  const country = countryCode || getCurrentCountryCode()

  return {
    home: generateCountryUrl('home', country),
    stores: generateCountryUrl('stores', country),
    coupons: generateCountryUrl('coupons', country),
    blog: generateCountryUrl('blog', country),
    about: generateCountryUrl('about', country),
    contact: generateCountryUrl('contact', country),
    help: generateCountryUrl('help', country),
    howItWorks: generateCountryUrl('how-it-works', country),
    terms: generateCountryUrl('terms', country),
    privacy: generateCountryUrl('privacy', country)
  }
}
