import React from 'react'
import { useParams, Navigate } from 'react-router-dom'
import { isValidCountryCode } from '../utils/countryUrl'

interface CountryRouteGuardProps {
  children: React.ReactNode
}

/**
 * Route guard that validates country codes and redirects invalid ones
 */
const CountryRouteGuard: React.FC<CountryRouteGuardProps> = ({ children }) => {
  const { country_code } = useParams<{ country_code?: string }>()
  
  // If there's a country code in the URL, validate it
  if (country_code && !isValidCountryCode(country_code)) {
    // Redirect to the same path but without the invalid country code
    const currentPath = window.location.pathname
    const pathSegments = currentPath.split('/').filter(Boolean)
    
    // Remove the first segment (invalid country code) and reconstruct the path
    if (pathSegments.length > 1) {
      const newPath = '/' + pathSegments.slice(1).join('/')
      return <Navigate to={newPath} replace />
    } else {
      // If only country code in path, redirect to home
      return <Navigate to="/home" replace />
    }
  }
  
  return <>{children}</>
}

export default CountryRouteGuard
